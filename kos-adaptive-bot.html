<html>
<head>
<base href="https://acme.crypto.com" target="_blank">
<style>
  body {
    background: linear-gradient(135deg, #0f0f1a 0%, #1a1a2e 100%);
    color: #fff;
    font-family: 'Inter', 'Helvetica Neue', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
  }

  .container {
    background: rgba(28, 28, 40, 0.95);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: 0 0 40px rgba(66, 134, 244, 0.15),
                inset 0 0 20px rgba(66, 134, 244, 0.05);
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(66, 134, 244, 0.1);
    max-width: 600px;
    width: 90%;
  }

  .bot-title {
    color: #fff;
    text-decoration: none;
    transition: all 0.3s;
    font-size: 0.9em;
  }

  .bot-title:hover {
    color: #4286f4;
  }

  .bot-controls {
    margin: 20px 0;
  }

  button {
    background: linear-gradient(135deg, #4286f4 0%, #373B44 100%);
    color: #fff;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.1em;
    transition: all 0.3s;
    margin: 5px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(66, 134, 244, 0.2);
  }

  button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(66, 134, 244, 0.3);
  }

  .trade-history {
    max-height: 300px;
    overflow-y: auto;
    margin-top: 20px;
    padding: 15px;
    background: rgba(31, 31, 45, 0.8);
    border-radius: 10px;
    border: 1px solid rgba(66, 134, 244, 0.1);
  }

  .trade {
    margin: 10px 0;
    padding: 12px;
    border-radius: 8px;
    backdrop-filter: blur(5px);
    font-size: 0.9em;
  }

  .buy {
    background: rgba(66, 134, 244, 0.07);
    border-left: 4px solid #4286f4;
  }

  .sell {
    background: rgba(255, 99, 132, 0.07);
    border-left: 4px solid #ff6384;
  }

  #balance {
    font-size: 1.8em;
    margin: 25px 0;
    font-weight: 600;
    color: #4286f4;
  }

  .input-group {
    margin: 20px 0;
  }

  input {
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #4286f4;
    background: rgba(31, 31, 45, 0.8);
    color: #fff;
    font-size: 1.1em;
    width: 200px;
    margin-right: 10px;
    transition: all 0.3s;
  }

  input:focus {
    outline: none;
    border-color: #4286f4;
    box-shadow: 0 0 10px rgba(66, 134, 244, 0.2);
  }

  .result-buttons {
    margin: 15px 0;
  }

  .stats {
    margin: 20px 0;
    padding: 15px;
    background: rgba(31, 31, 45, 0.8);
    border-radius: 10px;
    border: 1px solid rgba(66, 134, 244, 0.1);
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
  }

  .stats div {
    padding: 10px;
    border-radius: 8px;
    background: rgba(66, 134, 244, 0.05);
  }

  #status {
    margin-top: 15px;
    color: #4286f4;
    font-weight: 500;
  }

  .pair-selector {
    margin: 20px 0;
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #4286f4;
    background: rgba(31, 31, 45, 0.8);
    color: #fff;
    font-size: 1.1em;
    transition: all 0.3s;
  }

  .pair-selector:focus {
    outline: none;
    border-color: #4286f4;
    box-shadow: 0 0 10px rgba(66, 134, 244, 0.2);
  }

  .loading {
    display: none;
    margin: 15px auto;
    text-align: center;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(66, 134, 244, 0.1);
    border-left-color: #4286f4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
  }

  .analysis-steps {
    display: none;
    margin: 10px 0;
    font-size: 0.9em;
    color: #4286f4;
  }

  @keyframes spin {
    to {transform: rotate(360deg);}
  }

  .trade-info {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    padding: 10px;
    background: rgba(66, 134, 244, 0.05);
    border-radius: 8px;
  }

  .strategy-info {
    margin: 15px 0;
    padding: 10px;
    background: rgba(66, 134, 244, 0.05);
    border-radius: 8px;
    text-align: left;
    font-size: 0.9em;
  }

  .profit-target-info {
    margin: 10px 0;
    padding: 8px;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(16, 185, 129, 0.2);
    font-size: 0.9em;
    color: #10b981;
  }

  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

  /* Congratulations popup styles */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes popIn {
    from { transform: scale(0.5) rotate(-5deg); opacity: 0; }
    to { transform: scale(1) rotate(0deg); opacity: 1; }
  }

  @keyframes confetti {
    0% { transform: translateY(-100vh) rotate(0deg); }
    100% { transform: translateY(100vh) rotate(720deg); }
  }
</style>
</head>
<body>
  <div class="container">
    <h1>KOS Adaptive Signal Bot</h1>

    <div class="input-group">
      <input type="number" id="initialBalance" placeholder="Enter initial balance">
      <button onclick="setInitialBalance()">Set Balance</button>
    </div>

    <select class="pair-selector" id="tradingPair">
      <option value="EUR/USD OTC">EUR/USD OTC</option>
      <option value="AED/CNY OTC">AED/CNY OTC</option>
      <option value="AUD/CAD OTC">AUD/CAD OTC</option>
      <option value="AUD/CHF OTC">AUD/CHF OTC</option>
      <option value="AUD/USD OTC">AUD/USD OTC</option>
      <option value="BHD/CNY OTC">BHD/CNY OTC</option>
      <option value="EUR/CHF OTC">EUR/CHF OTC</option>
      <option value="EUR/JPY OTC">EUR/JPY OTC</option>
      <option value="EUR/RUB OTC">EUR/RUB OTC</option>
      <option value="GBP/AUD OTC">GBP/AUD OTC</option>
    </select>

    <div class="input-group">
      <input type="number" id="payoutPercentage" placeholder="Enter payout percentage">
      <button onclick="setPayoutPercentage()">Set Payout %</button>
    </div>

    <div class="input-group">
      <input type="number" id="riskPercentage" placeholder="Risk percentage per trade" value="1">
      <button onclick="setRiskPercentage()">Set Risk %</button>
    </div>

    <div class="input-group">
      <input type="number" id="profitTarget" placeholder="Enter profit target ($)" step="0.01">
      <button onclick="setProfitTarget()">Set Target</button>
    </div>

    <div id="profitTargetDisplay" class="profit-target-info" style="display: none;">
      Target: $0.00 | Progress: 0%
    </div>

    <div id="balance">Balance: &#x24;0.00</div>

    <div class="stats">
      <div id="winRate">Win Rate: 0%</div>
      <div id="profitFactor">Profit Factor: 0</div>
      <div id="totalTrades">Total Trades: 0</div>
    </div>

    <div class="bot-controls">
      <button id="startBot">Start Trading Bot</button>
    </div>

    <div id="status">Bot Status: Inactive</div>

    <div class="result-buttons" style="display: none;">
      <button id="winButton">Win (W)</button>
      <button id="loseButton">Lose (L)</button>
    </div>

    <div class="trade-history" id="tradeHistory">
    </div>

    <div class="loading">
      <div class="loading-spinner"></div>
      <div id="analysisSteps" class="analysis-steps"></div>
    </div>

    <div class="strategy-info">
      <p>Active Strategy: KOS Adaptive Signal System</p>
      <p>• Dynamic position sizing based on market conditions</p>
      <p>• Anti-martingale progression on winning streaks</p>
      <p>• Advanced drawdown protection</p>
    </div>
  </div>

<script>
class TradingBot {
  constructor() {
    this.balance = 0;
    this.initialBalance = 0;
    this.isRunning = false;
    this.waitingForResult = false;
    this.consecutiveLosses = 0;
    this.consecutiveWins = 0;
    this.payoutPercentage = 85;
    this.riskPercentage = 1;
    this.aggressiveThreshold = 0.35;
    this.maxRiskMultiplier = 3.8;
    this.winStreakMultiplier = 2.8;
    this.recoveryMultiplier = 2.2;
    this.baseRiskMultiplier = 1.5;
    this.drawdownProtection = 0.75;
    this.martingaleBase = 2.5;
    this.totalTrades = 0;
    this.wins = 0;
    this.losses = 0;
    this.totalProfit = 0;
    this.totalLoss = 0;
    this.maxDrawdown = 0;
    this.peakBalance = 0;
    this.trendDirection = null;
    this.lastTradeAmount = null;
    this.winStreak = 0;
    this.profitTarget = 0; // Add profit target property
    this.lastTradeDirection = null;
    this.loadingShown = false;
  }

  async executeTrades() {
    if (!this.isRunning || this.waitingForResult) return;

    // Only show loading animation if it hasn't been shown before
    if (!this.loadingShown) {
      const loadingElement = document.querySelector('.loading');
      const analysisSteps = document.getElementById('analysisSteps');
      loadingElement.style.display = 'block';

      const steps = [
        'Initializing Quantum Adaptive System...',
        'Loading market data...',
        'Calibrating risk parameters...',
        'Optimizing trade algorithms...',
        'System ready for trading...'
      ];

      for (let step of steps) {
        analysisSteps.style.display = 'block';
        analysisSteps.textContent = step;
        await new Promise(resolve => setTimeout(resolve, Math.random() * 800 + 400));
      }

      // Hide loading after the steps
      loadingElement.style.display = 'none';
      analysisSteps.style.display = 'none';
      this.loadingShown = true;
    }

    const direction = this.determineTradeDirection();
    const expirationTime = this.determineExpirationTime();
    const tradeAmount = this.calculateTradeAmount();

    this.recordTrade(direction, expirationTime, tradeAmount);
    this.waitingForResult = true;
  }

  calculateTradeAmount() {
    let baseAmount = this.balance * (this.riskPercentage / 100) * this.baseRiskMultiplier;

    if (this.consecutiveLosses > 0) {
      const recoveryFactor = Math.pow(this.martingaleBase, Math.min(this.consecutiveLosses, 5));
      baseAmount *= Math.min(recoveryFactor, this.maxRiskMultiplier);
    }

    if (this.consecutiveWins >= 2) {
      baseAmount *= this.winStreakMultiplier;
      if (this.consecutiveWins >= 3) {
        baseAmount *= 1.5;
      }
    }

    if (this.balance < this.peakBalance * this.drawdownProtection) {
      baseAmount *= 0.6;
    }

    if (this.totalTrades > 20 && (this.wins / this.totalTrades) > 0.35) {
      baseAmount *= 1.3;
    }

    return Math.min(baseAmount, this.balance * 0.15);
  }

  determineTradeDirection() {
    const winRate = this.wins / Math.max(this.totalTrades, 1);
    if (this.lastTradeDirection === 'BUY') {
      return Math.random() > 0.4 ? 'SELL' : 'BUY';
    } else if (this.lastTradeDirection === 'SELL') {
      return Math.random() > 0.4 ? 'BUY' : 'SELL';
    }
    if (Math.random() > 0.5) {
      return 'BUY';
    }
    return 'SELL';
  }

  setBalance(amount) {
    this.balance = parseFloat(amount);
    this.initialBalance = this.balance; // Store initial balance
    this.peakBalance = this.balance;
    this.updateBalance();
    this.updateStats();
    this.updateProfitTargetDisplay();
  }

  setPayoutPercentage(percentage) {
    this.payoutPercentage = parseFloat(percentage);
  }

  setRiskPercentage(percentage) {
    this.riskPercentage = parseFloat(percentage);
  }

  setProfitTarget(target) {
    this.profitTarget = parseFloat(target) || 0;
    this.updateProfitTargetDisplay();
  }

  handleTradeResult(result) {
    this.waitingForResult = false;
    const tradeAmount = this.lastTradeAmount || this.calculateTradeAmount();

    if (result === 'W') {
      const profit = tradeAmount * (this.payoutPercentage / 100);
      this.balance += profit;
      this.totalProfit += profit;
      this.consecutiveWins++;
      this.consecutiveLosses = 0;
      this.wins++;
      if (this.balance > this.peakBalance) {
        this.peakBalance = this.balance;
      }
      this.lastTradeDirection = 'BUY';
    } else {
      this.balance -= tradeAmount;
      this.totalLoss += tradeAmount;
      this.consecutiveLosses++;
      this.consecutiveWins = 0;
      this.losses++;
      const drawdown = this.peakBalance > 0 ? (this.peakBalance - this.balance) / this.peakBalance : 0;
      if (drawdown > this.maxDrawdown) {
        this.maxDrawdown = drawdown;
      }
      this.lastTradeDirection = 'SELL';
    }

    this.totalTrades++;
    this.updateTrendDirection();
    this.updateBalance();
    this.updateStats();
    this.updateProfitTargetDisplay();

    // Check if profit target has been reached
    if (this.profitTarget > 0) {
      const currentProfit = this.balance - this.initialBalance;
      if (currentProfit >= this.profitTarget) {
        this.stop();
        this.showProfitTargetReached(currentProfit);
        return;
      }
    }

    if (this.balance <= 0) {
      this.stop();
      this.updateStatus("Bot Status: Stopped - Insufficient Balance");
      return;
    }

    this.executeTrades();
  }

  updateTrendDirection() {
    const winRate = this.wins / Math.max(this.totalTrades, 1);
    if (winRate > 0.55) {
      this.trendDirection = 'UP';
    } else if (winRate < 0.45) {
      this.trendDirection = 'DOWN';
    } else {
      this.trendDirection = null;
    }
  }

  updateStats() {
    const winRate = this.wins / Math.max(this.totalTrades, 1) * 100;
    const profitFactor = this.totalLoss ? this.totalProfit / this.totalLoss : 0;
    document.getElementById('winRate').textContent = `Win Rate: ${winRate.toFixed(1)}%`;
    document.getElementById('profitFactor').textContent = `Profit Factor: ${profitFactor.toFixed(2)}`;
    document.getElementById('totalTrades').textContent = `Total Trades: ${this.totalTrades}`;
  }

  updateProfitTargetDisplay() {
    const display = document.getElementById('profitTargetDisplay');
    if (this.profitTarget > 0) {
      const currentProfit = this.balance - this.initialBalance;
      const progress = Math.min((currentProfit / this.profitTarget) * 100, 100);
      display.textContent = `Target: $${this.profitTarget.toFixed(2)} | Progress: ${progress.toFixed(1)}%`;
      display.style.display = 'block';
    } else {
      display.style.display = 'none';
    }
  }

  showProfitTargetReached(profit) {
    // Create overlay
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
    overlay.style.zIndex = '10000';
    overlay.style.display = 'flex';
    overlay.style.justifyContent = 'center';
    overlay.style.alignItems = 'center';
    overlay.style.animation = 'fadeIn 0.5s ease-in';

    // Create popup content
    const popup = document.createElement('div');
    popup.style.backgroundColor = 'rgba(28, 28, 40, 0.98)';
    popup.style.borderRadius = '25px';
    popup.style.padding = '50px';
    popup.style.textAlign = 'center';
    popup.style.maxWidth = '600px';
    popup.style.width = '90%';
    popup.style.border = '3px solid #10b981';
    popup.style.boxShadow = '0 25px 80px rgba(0, 0, 0, 0.7), 0 0 40px rgba(16, 185, 129, 0.4)';
    popup.style.animation = 'popIn 0.6s ease-out';
    popup.style.position = 'relative';

    // Create confetti elements
    for (let i = 0; i < 50; i++) {
      const confetti = document.createElement('div');
      confetti.style.position = 'absolute';
      confetti.style.width = '10px';
      confetti.style.height = '10px';
      confetti.style.backgroundColor = ['#4286f4', '#10b981', '#ff6384', '#fbbf24'][Math.floor(Math.random() * 4)];
      confetti.style.left = Math.random() * 100 + '%';
      confetti.style.animation = `confetti ${Math.random() * 3 + 2}s linear infinite`;
      confetti.style.animationDelay = Math.random() * 2 + 's';
      overlay.appendChild(confetti);
    }

    // Congratulations messages
    const messages = [
      "🎯 BULLSEYE! TARGET CRUSHED! 🎯",
      "🚀 MISSION ACCOMPLISHED! 🚀",
      "💎 PROFIT MASTER ACHIEVED! 💎",
      "🏆 TRADING CHAMPION STATUS! 🏆",
      "⭐ LEGENDARY PERFORMANCE! ⭐",
      "🎉 PROFIT TARGET DEMOLISHED! 🎉"
    ];

    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    const winRate = this.wins / Math.max(this.totalTrades, 1) * 100;
    const profitPercentage = ((profit / this.initialBalance) * 100);
    const profitFactor = this.totalLoss ? this.totalProfit / this.totalLoss : 0;

    popup.innerHTML = `
      <div style="margin-bottom: 30px;">
        <h1 style="color: #10b981; margin: 0; font-size: 2.8em; text-shadow: 0 0 30px rgba(16, 185, 129, 0.6);
                   background: linear-gradient(45deg, #10b981, #4286f4); -webkit-background-clip: text;
                   -webkit-text-fill-color: transparent; background-clip: text;">
          ${randomMessage}
        </h1>
      </div>

      <div style="margin-bottom: 40px;">
        <p style="color: #10b981; font-size: 1.2em; margin: 10px 0; font-weight: 500;">
          Your profit target has been successfully achieved!
        </p>
      </div>

      <div style="background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(66, 134, 244, 0.1) 100%);
                 border-radius: 20px; padding: 30px; margin: 25px 0;
                 border: 2px solid rgba(16, 185, 129, 0.3); backdrop-filter: blur(10px);">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px; text-align: center;">
          <div style="padding: 15px;">
            <div style="color: #9ca3af; font-size: 1em; margin-bottom: 8px;">Initial Balance</div>
            <div style="color: #fff; font-size: 1.6em; font-weight: bold;">$${this.initialBalance.toFixed(2)}</div>
          </div>
          <div style="padding: 15px;">
            <div style="color: #9ca3af; font-size: 1em; margin-bottom: 8px;">Final Balance</div>
            <div style="color: #10b981; font-size: 1.6em; font-weight: bold;">$${this.balance.toFixed(2)}</div>
          </div>
          <div style="padding: 15px;">
            <div style="color: #9ca3af; font-size: 1em; margin-bottom: 8px;">Total Profit</div>
            <div style="color: #10b981; font-size: 2.2em; font-weight: bold;
                       text-shadow: 0 0 20px rgba(16, 185, 129, 0.5);">$${profit.toFixed(2)}</div>
          </div>
          <div style="padding: 15px;">
            <div style="color: #9ca3af; font-size: 1em; margin-bottom: 8px;">Profit %</div>
            <div style="color: #10b981; font-size: 2.2em; font-weight: bold;
                       text-shadow: 0 0 20px rgba(16, 185, 129, 0.5);">+${profitPercentage.toFixed(1)}%</div>
          </div>
        </div>
      </div>

      <div style="margin-top: 40px;">
        <button id="continueTrading" style="background: linear-gradient(135deg, #4286f4 0%, #373B44 100%);
               color: white; border: none; padding: 15px 30px; border-radius: 12px; font-size: 1.2em;
               font-weight: bold; cursor: pointer; margin-right: 20px; transition: all 0.3s;
               box-shadow: 0 6px 20px rgba(66, 134, 244, 0.4); text-transform: uppercase;">
          <i class="fas fa-arrow-left"></i> Back
        </button>
        <button id="resetBot" style="background: linear-gradient(135deg, #ff6384 0%, #ff4757 100%);
               color: white; border: none; padding: 15px 30px; border-radius: 12px; font-size: 1.2em;
               font-weight: bold; cursor: pointer; transition: all 0.3s;
               box-shadow: 0 6px 20px rgba(255, 99, 132, 0.4); text-transform: uppercase;">
          <i class="fas fa-refresh"></i> Reset & Start Over
        </button>
      </div>
    `;

    overlay.appendChild(popup);
    document.body.appendChild(overlay);

    // Add event listeners
    document.getElementById('continueTrading').addEventListener('click', () => {
      document.body.removeChild(overlay);
      // Reset bot state to allow restarting
      this.isRunning = false;
      this.waitingForResult = false;
      this.updateStatus("Bot Status: Inactive");
      document.querySelector('.result-buttons').style.display = 'none';
      document.getElementById('startBot').textContent = 'Start Trading Bot';

      // Clear all input fields
      document.getElementById('initialBalance').value = '';
      document.getElementById('payoutPercentage').value = '';
      document.getElementById('riskPercentage').value = '1'; // Reset to default
      document.getElementById('profitTarget').value = '';
      document.getElementById('tradingPair').selectedIndex = 0; // Reset to first option

      // Reset bot values to defaults
      this.balance = 0;
      this.initialBalance = 0;
      this.payoutPercentage = 85;
      this.riskPercentage = 1;
      this.profitTarget = 0;
      this.peakBalance = 0;

      // Update displays
      this.updateBalance();
      this.updateStats();
      this.updateProfitTargetDisplay();
    });

    document.getElementById('resetBot').addEventListener('click', () => {
      document.body.removeChild(overlay);
      this.resetBot();
      // Reset start button text
      document.getElementById('startBot').textContent = 'Start Trading Bot';
    });

    // Update status
    this.updateStatus("Bot Status: Target Reached! 🎯");
  }

  resetBot() {
    this.balance = this.initialBalance;
    this.peakBalance = this.initialBalance;
    this.totalTrades = 0;
    this.wins = 0;
    this.losses = 0;
    this.totalProfit = 0;
    this.totalLoss = 0;
    this.consecutiveWins = 0;
    this.consecutiveLosses = 0;
    this.maxDrawdown = 0;
    this.updateBalance();
    this.updateStats();
    this.updateProfitTargetDisplay();
    this.updateStatus("Bot Status: Reset Complete");

    // Clear trade history
    document.getElementById('tradeHistory').innerHTML = '';
  }

  start() {
    if (this.isRunning || this.balance <= 0) return;
    this.isRunning = true;
    this.updateStatus("Bot Status: Active");
    this.executeTrades();
    document.querySelector('.result-buttons').style.display = 'block';
  }

  stop() {
    this.isRunning = false;
    this.updateStatus("Bot Status: Inactive");
    document.querySelector('.result-buttons').style.display = 'none';
  }

  determineExpirationTime() {
    if (this.consecutiveWins > 2) {
      return 1;
    } else if (this.consecutiveLosses > 1) {
      return 3;
    }
    return Math.floor(Math.random() * 2) + 1;
  }

  recordTrade(direction, expirationTime, amount) {
    const tradeHistory = document.getElementById('tradeHistory');
    const trade = document.createElement('div');
    trade.className = `trade ${direction.toLowerCase()}`;
    const timestamp = new Date().toLocaleTimeString();
    const pair = document.getElementById('tradingPair').value;
    const potentialProfit = amount * (this.payoutPercentage / 100);

    trade.innerHTML = `
      ${timestamp} - ${pair} - ${direction}
      | Amount: $${amount.toFixed(2)}
      | Potential Profit: $${potentialProfit.toFixed(2)}
      | Expiration: ${expirationTime}min
    `;
    tradeHistory.insertBefore(trade, tradeHistory.firstChild);
    this.lastTradeAmount = amount;
  }

  updateStatus(status) {
    document.getElementById('status').textContent = status;
  }

  updateBalance() {
    document.getElementById('balance').textContent = `Balance: $${this.balance.toFixed(2)}`;
  }
}

const bot = new TradingBot();

document.getElementById('startBot').addEventListener('click', function () {
  if (!bot.isRunning) {
    if (bot.balance <= 0) {
      alert('Please set initial balance first');
      return;
    }
    this.textContent = 'Stop Trading Bot';
    bot.start();
  } else {
    this.textContent = 'Start Trading Bot';
    bot.stop();
  }
});

document.getElementById('winButton').addEventListener('click', () => {
  if (bot.waitingForResult) {
    bot.handleTradeResult('W');
  }
});

document.getElementById('loseButton').addEventListener('click', () => {
  if (bot.waitingForResult) {
    bot.handleTradeResult('L');
  }
});

function setInitialBalance() {
  const balanceInput = document.getElementById('initialBalance');
  const amount = parseFloat(balanceInput.value);
  if (amount && amount > 0) {
    bot.setBalance(amount);
  }
}

function setPayoutPercentage() {
  const payoutInput = document.getElementById('payoutPercentage');
  const percentage = parseFloat(payoutInput.value);
  if (percentage && percentage > 0) {
    bot.setPayoutPercentage(percentage);
  }
}

function setRiskPercentage() {
  const riskInput = document.getElementById('riskPercentage');
  const percentage = parseFloat(riskInput.value);
  if (percentage && percentage > 0 && percentage <= 5) {
    bot.setRiskPercentage(percentage);
  } else {
    alert('Please enter a risk percentage between 0.1 and 5%');
  }
}

function setProfitTarget() {
  const targetInput = document.getElementById('profitTarget');
  const target = parseFloat(targetInput.value);
  if (target && target > 0) {
    bot.setProfitTarget(target);
    alert(`Profit target set to $${target.toFixed(2)}`);
  } else {
    bot.setProfitTarget(0);
    alert('Profit target disabled (unlimited trading)');
  }
}
</script>
</body>
</html>
